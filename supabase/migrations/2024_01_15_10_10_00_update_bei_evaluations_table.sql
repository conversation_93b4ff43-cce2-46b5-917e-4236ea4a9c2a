-- Update the bei_evaluations table to match LGD pattern
-- Add missing columns for prompt versioning and content

-- Add formattedInput column
ALTER TABLE bei_evaluations ADD COLUMN "formattedInput" TEXT;

-- Change output column type from TEXT to JSONB to match LGD
ALTER TABLE bei_evaluations ALTER COLUMN output TYPE JSONB USING output::jsonb;

-- Add prompt version and content columns
ALTER TABLE bei_evaluations ADD COLUMN "prompt1Version" INT;
ALTER TABLE bei_evaluations ADD COLUMN "prompt2Version" INT;
ALTER TABLE bei_evaluations ADD COLUMN "prompt1Content" TEXT;
ALTER TABLE bei_evaluations ADD COLUMN "prompt2Content" TEXT;

-- Update existing records to have proper status if needed
UPDATE bei_evaluations SET status = 'in_progress' WHERE status IS NULL;
